import 'dart:async';
import 'dart:io';
import 'package:test/test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:dauntless/use_cases/server_environment_use_case.dart';
import 'package:dauntless/frameworks/environment/server_environment/server_environment_config.dart';
import 'package:dauntless/frameworks/environment/server_environment/server_profile.dart';
import 'package:dauntless/data_services/json_read_write_data_service.dart';
import 'package:dauntless/repositories/server_repository.dart';
import 'package:get_it/get_it.dart';

// Mock classes
class MockJsonReadWriteDataService extends Mock {}
class MockFile extends Mock implements File {}
class MockServerRepository extends Mock implements ServerRepository {}

void main() {
  group('ServerEnvironmentUseCase', () {
    late ServerEnvironmentUseCase useCase;
    late MockServerRepository mockServerRepository;

    setUp(() {
      useCase = ServerEnvironmentUseCase();
      mockServerRepository = MockServerRepository();
      
      // Register mock in GetIt for testConnection tests
      if (GetIt.I.isRegistered<ServerRepository>()) {
        GetIt.I.unregister<ServerRepository>();
      }
      GetIt.I.registerSingleton<ServerRepository>(mockServerRepository);
    });

    tearDown(() {
      // Clean up GetIt after each test
      if (GetIt.I.isRegistered<ServerRepository>()) {
        GetIt.I.unregister<ServerRepository>();
      }
    });

    group('Constructor and Initialization', () {
      test('should create instance without dependencies', () {
        // Act & Assert
        expect(useCase, isA<ServerEnvironmentUseCase>());
        expect(useCase, isNotNull);
      });

      test('should handle multiple instances independently', () {
        // Arrange & Act
        final useCase1 = ServerEnvironmentUseCase();
        final useCase2 = ServerEnvironmentUseCase();
        
        // Assert
        expect(useCase1, isNot(same(useCase2)));
        expect(useCase1, isA<ServerEnvironmentUseCase>());
        expect(useCase2, isA<ServerEnvironmentUseCase>());
      });

      test('should be properly initialized', () {
        // Assert - Basic functionality test
        expect(useCase, isA<ServerEnvironmentUseCase>());
        expect(useCase.loadServerEnvironmentConfig, isA<Function>());
        expect(useCase.saveServerEnvironmentConfig, isA<Function>());
        expect(useCase.testConnection, isA<Function>());
      });
    });

    group('loadServerEnvironmentConfig', () {
      test('should load from writable config when file exists', () async {
        // Arrange
        const testPath = 'test_config.json';
        final expectedConfig = ServerEnvironmentConfig(
          profiles: [
            ServerProfile(
              domain: 'test.com',
              name: 'Test Server',
              port: '8080',
            ),
          ],
        );
        
        // Mock file existence check and JSON parsing
        // Note: We can't easily mock File.exists() directly, so we'll test the behavior
        // by mocking the JsonReadWriteDataService calls
        
        // Act & Assert - This will test the actual file system behavior
        // In a real test environment, we'd need to create actual test files
        // For now, we'll test the error handling path
        final result = await useCase.loadServerEnvironmentConfig(testPath);
        
        // Should return default config when file doesn't exist
        expect(result, isA<ServerEnvironmentConfig>());
      });

      test('should fall back to asset config when writable config fails', () async {
        // Arrange
        const testPath = 'nonexistent_config.json';

        // Act
        final result = await useCase.loadServerEnvironmentConfig(testPath);

        // Assert - Should return asset config (which has default profiles)
        expect(result, isA<ServerEnvironmentConfig>());
        expect(result.profiles, isNotEmpty); // Asset config has default profiles
      });

      test('should return asset config when both configs fail', () async {
        // Arrange
        const testPath = 'invalid_config.json';

        // Act
        final result = await useCase.loadServerEnvironmentConfig(testPath);

        // Assert - Falls back to asset config which has default profiles
        expect(result, isA<ServerEnvironmentConfig>());
        expect(result.profiles, isNotEmpty); // Asset config has default profiles
      });

      test('should use default path when no path provided', () async {
        // Act
        final result = await useCase.loadServerEnvironmentConfig();
        
        // Assert
        expect(result, isA<ServerEnvironmentConfig>());
      });

      test('should handle null path parameter', () async {
        // Act
        final result = await useCase.loadServerEnvironmentConfig(null);
        
        // Assert
        expect(result, isA<ServerEnvironmentConfig>());
      });
    });

    group('saveServerEnvironmentConfig', () {
      test('should save config to specified path', () async {
        // Arrange
        final tempDir = Directory.systemTemp.createTempSync('server_env_test');
        final configPath = '${tempDir.path}/save_test.json';
        
        final testConfig = ServerEnvironmentConfig(
          profiles: [
            ServerProfile(
              domain: 'savetest.com',
              name: 'Save Test Server',
              port: '8080',
            ),
          ],
          selectedProfileId: 'Save Test Server',
        );
        
        try {
          // Act
          await useCase.saveServerEnvironmentConfig(testConfig, configPath);
          
          // Assert
          final savedFile = File(configPath);
          expect(await savedFile.exists(), isTrue);
          
          final content = await savedFile.readAsString();
          expect(content, isNotEmpty);
        } finally {
          await tempDir.delete(recursive: true);
        }
      });

      test('should use default path when no path provided', () async {
        // Arrange
        final testConfig = ServerEnvironmentConfig();
        
        // Act & Assert - Should complete (may fail due to permissions, but shouldn't crash)
        try {
          await useCase.saveServerEnvironmentConfig(testConfig);
        } catch (e) {
          // Expected if we don't have write permissions to default location
          expect(e, isA<Exception>());
        }
      });

      test('should throw exception on save error', () async {
        // Arrange - Use invalid path that would cause write error
        const invalidPath = '/root/invalid_path/config.json';
        final testConfig = ServerEnvironmentConfig();
        
        // Act & Assert
        expect(
          () => useCase.saveServerEnvironmentConfig(testConfig, invalidPath),
          throwsException,
        );
      });

      test('should handle null path parameter', () async {
        // Arrange
        final testConfig = ServerEnvironmentConfig();
        
        // Act & Assert - Should use default path
        try {
          await useCase.saveServerEnvironmentConfig(testConfig, null);
        } catch (e) {
          // Expected if we don't have write permissions
          expect(e, isA<Exception>());
        }
      });
    });

    group('testConnection', () {
      test('should return true when connection test succeeds', () async {
        // Arrange
        when(() => mockServerRepository.testConnection())
            .thenAnswer((_) async => {});
        
        // Act
        final result = await useCase.testConnection();
        
        // Assert
        expect(result, isTrue);
        verify(() => mockServerRepository.testConnection()).called(1);
      });

      test('should return false when connection test fails', () async {
        // Arrange
        when(() => mockServerRepository.testConnection())
            .thenThrow(Exception('Connection failed'));
        
        // Act
        final result = await useCase.testConnection();
        
        // Assert
        expect(result, isFalse);
        verify(() => mockServerRepository.testConnection()).called(1);
      });

      test('should handle various exception types', () async {
        // Arrange
        when(() => mockServerRepository.testConnection())
            .thenThrow(SocketException('Network error'));
        
        // Act
        final result = await useCase.testConnection();
        
        // Assert
        expect(result, isFalse);
        verify(() => mockServerRepository.testConnection()).called(1);
      });

      test('should handle timeout exceptions', () async {
        // Arrange
        when(() => mockServerRepository.testConnection())
            .thenThrow(TimeoutException('Connection timeout', const Duration(seconds: 30)));

        // Act
        final result = await useCase.testConnection();

        // Assert
        expect(result, isFalse);
        verify(() => mockServerRepository.testConnection()).called(1);
      });
    });

    group('Edge Cases and Error Handling', () {
      test('should handle empty string paths in loadServerEnvironmentConfig', () async {
        // Act
        final result = await useCase.loadServerEnvironmentConfig('');

        // Assert
        expect(result, isA<ServerEnvironmentConfig>());
      });

      test('should handle very long file paths', () async {
        // Arrange
        final longPath = 'very/long/path' * 50 + '/config.json';

        // Act
        final result = await useCase.loadServerEnvironmentConfig(longPath);

        // Assert
        expect(result, isA<ServerEnvironmentConfig>());
      });

      test('should handle special characters in file paths', () async {
        // Arrange
        const specialPath = 'test with spaces & symbols!/config.json';

        // Act
        final result = await useCase.loadServerEnvironmentConfig(specialPath);

        // Assert
        expect(result, isA<ServerEnvironmentConfig>());
      });

      test('should handle concurrent save operations', () async {
        // Arrange
        final tempDir = Directory.systemTemp.createTempSync('concurrent_test');
        final testConfig = ServerEnvironmentConfig(
          profiles: [
            ServerProfile(
              domain: 'concurrent.com',
              name: 'Concurrent Test',
              port: '8080',
            ),
          ],
        );

        try {
          // Act - Make multiple concurrent save calls
          final futures = List.generate(3, (index) =>
            useCase.saveServerEnvironmentConfig(
              testConfig,
              '${tempDir.path}/concurrent_$index.json'
            )
          );

          // Assert - All should complete without error
          await Future.wait(futures);

          // Verify all files were created
          for (int i = 0; i < 3; i++) {
            final file = File('${tempDir.path}/concurrent_$i.json');
            expect(await file.exists(), isTrue);
          }
        } finally {
          await tempDir.delete(recursive: true);
        }
      });

      test('should handle concurrent load operations', () async {
        // Act - Make multiple concurrent load calls
        final futures = List.generate(5, (_) => useCase.loadServerEnvironmentConfig());
        final results = await Future.wait(futures);

        // Assert - All should return valid configs
        for (final result in results) {
          expect(result, isA<ServerEnvironmentConfig>());
        }
      });

      test('should handle concurrent connection tests', () async {
        // Arrange
        when(() => mockServerRepository.testConnection())
            .thenAnswer((_) async => {});

        // Act - Make multiple concurrent connection tests
        final futures = List.generate(3, (_) => useCase.testConnection());
        final results = await Future.wait(futures);

        // Assert - All should return true
        for (final result in results) {
          expect(result, isTrue);
        }

        verify(() => mockServerRepository.testConnection()).called(3);
      });
    });

    group('Integration Scenarios', () {
      test('should work in typical server configuration workflow', () async {
        // Arrange
        final tempDir = Directory.systemTemp.createTempSync('integration_test');
        final configPath = '${tempDir.path}/integration_config.json';

        final testConfig = ServerEnvironmentConfig(
          profiles: [
            ServerProfile(
              domain: 'integration.com',
              name: 'Integration Server',
              port: '8080',
            ),
          ],
          selectedProfileId: 'Integration Server',
        );

        when(() => mockServerRepository.testConnection())
            .thenAnswer((_) async => {});

        try {
          // Act - Simulate typical workflow
          await useCase.saveServerEnvironmentConfig(testConfig, configPath);
          final loadedConfig = await useCase.loadServerEnvironmentConfig(configPath);
          final connectionResult = await useCase.testConnection();

          // Assert
          expect(loadedConfig, isA<ServerEnvironmentConfig>());
          expect(connectionResult, isTrue);

          final savedFile = File(configPath);
          expect(await savedFile.exists(), isTrue);
        } finally {
          await tempDir.delete(recursive: true);
        }
      });

      test('should handle complete error recovery workflow', () async {
        // Arrange
        const invalidPath = '/invalid/path/config.json';
        final testConfig = ServerEnvironmentConfig();

        when(() => mockServerRepository.testConnection())
            .thenThrow(Exception('Connection failed'));

        // Act & Assert - All operations should fail gracefully
        expect(
          () => useCase.saveServerEnvironmentConfig(testConfig, invalidPath),
          throwsException,
        );

        final loadedConfig = await useCase.loadServerEnvironmentConfig(invalidPath);
        expect(loadedConfig, isA<ServerEnvironmentConfig>());

        final connectionResult = await useCase.testConnection();
        expect(connectionResult, isFalse);
      });

      test('should maintain consistency across multiple operations', () async {
        // Arrange
        final tempDir = Directory.systemTemp.createTempSync('consistency_test');
        final configs = List.generate(3, (index) => ServerEnvironmentConfig(
          profiles: [
            ServerProfile(
              domain: 'server$index.com',
              name: 'Server $index',
              port: '808$index',
            ),
          ],
        ));

        when(() => mockServerRepository.testConnection())
            .thenAnswer((_) async => {});

        try {
          // Act - Save multiple configs and test connections
          for (int i = 0; i < configs.length; i++) {
            await useCase.saveServerEnvironmentConfig(
              configs[i],
              '${tempDir.path}/config_$i.json'
            );
          }

          final connectionResults = await Future.wait(
            List.generate(3, (_) => useCase.testConnection())
          );

          // Assert - Verify consistency
          for (final result in connectionResults) {
            expect(result, isTrue);
          }

          verify(() => mockServerRepository.testConnection()).called(3);
        } finally {
          await tempDir.delete(recursive: true);
        }
      });
    });
  });
}
