import 'package:collection/collection.dart';
import 'package:dauntless/models/base/game_card.dart';
import 'package:dauntless/use_cases/players_use_case.dart';

/// Use case for managing card parent-child relationships and hierarchies.
///
/// This use case handles finding cards that are "children" of other cards,
/// typically based on location relationships (e.g., vehicles in a location,
/// buildings in a sector, etc.).
///
/// This use case works with instantiated GameCard objects that have locationId
/// values, typically from game state or match state.
class CardChildrenUseCase {
  /// Creates a new CardChildrenUseCase instance.
  CardChildrenUseCase();

  /// Gets all child cards for a given parent card ID, organized by card type.
  ///
  /// Returns a map where keys are CardType and values are lists of GameCards
  /// that have the given [parentCardId] as their locationId.
  ///
  /// [allCards] should be a flat list of all instantiated GameCard objects,
  /// typically from game state hands.
  Map<CardType, List<GameCard>> getAllCardChildren(
    GameCardId parentCardId,
    List<GameCard> allCards,
  ) {
    final children = <CardType, List<GameCard>>{};

    // Initialize empty lists for all card types
    for (final cardType in CardType.values) {
      children[cardType] = <GameCard>[];
    }

    // Filter cards that have the parent as their location
    for (final card in allCards) {
      if (card.locationId == parentCardId) {
        children[card.type]!.add(card);
      }
    }

    return children;
  }

  /// Gets child cards of a specific type for a given parent card ID.
  ///
  /// Returns a list of GameCards of the specified [cardType] that have
  /// the given [parentCardId] as their locationId.
  ///
  /// [allCards] should be a flat list of all instantiated GameCard objects,
  /// typically from game state hands.
  List<GameCard> getCardChildren(
    GameCardId parentCardId,
    CardType cardType,
    List<GameCard> allCards,
  ) {
    return allCards
        .where((card) => card.locationId == parentCardId && card.type == cardType)
        .toList();
  }

  /// Gets all cards that have any location relationship (parent-child).
  ///
  /// Returns a map where keys are parent card IDs and values are maps
  /// of card types to lists of child cards.
  ///
  /// [allCards] should be a flat list of all instantiated GameCard objects,
  /// typically from game state hands.
  Map<GameCardId, Map<CardType, List<GameCard>>> getAllCardRelationships(
    List<GameCard> allCards,
  ) {
    final relationships = <GameCardId, Map<CardType, List<GameCard>>>{};

    // Group cards by their location (parent)
    for (final card in allCards) {
      if (card.locationId != null) {
        final parentId = card.locationId!;

        if (!relationships.containsKey(parentId)) {
          relationships[parentId] = <CardType, List<GameCard>>{};
        }

        if (!relationships[parentId]!.containsKey(card.type)) {
          relationships[parentId]![card.type] = <GameCard>[];
        }

        relationships[parentId]![card.type]!.add(card);
      }
    }

    return relationships;
  }

  /// Gets all cards that are direct children of the specified parent.
  ///
  /// This is a convenience method that filters cards by locationId only,
  /// regardless of card type.
  ///
  /// [allCards] should be a flat list of all instantiated GameCard objects,
  /// typically from game state hands.
  List<GameCard> getDirectChildren(
    GameCardId parentCardId,
    List<GameCard> allCards,
  ) {
    return allCards
        .where((card) => card.locationId == parentCardId)
        .toList();
  }

  /// Gets all cards that are descendants of the specified parent (recursive).
  ///
  /// This method finds all cards that are children, grandchildren, etc.
  /// of the specified parent card.
  ///
  /// [allCards] should be a flat list of all instantiated GameCard objects,
  /// typically from game state hands.
  List<GameCard> getAllDescendants(
    GameCardId parentCardId,
    List<GameCard> allCards,
  ) {
    return _getAllDescendantsWithVisited(parentCardId, allCards, <GameCardId>{});
  }

  /// Internal helper method that tracks visited cards to prevent infinite recursion.
  List<GameCard> _getAllDescendantsWithVisited(
    GameCardId parentCardId,
    List<GameCard> allCards,
    Set<GameCardId> visited,
  ) {
    // Prevent infinite recursion by tracking visited cards
    if (visited.contains(parentCardId)) {
      return <GameCard>[];
    }

    visited.add(parentCardId);

    final descendants = <GameCard>[];
    final directChildren = getDirectChildren(parentCardId, allCards);

    descendants.addAll(directChildren);

    // Recursively find descendants of each child
    for (final child in directChildren) {
      final childDescendants = _getAllDescendantsWithVisited(child.id, allCards, visited);
      descendants.addAll(childDescendants);
    }

    visited.remove(parentCardId); // Allow revisiting in different branches

    return descendants;
  }

  /// Gets the parent card of the specified child card.
  ///
  /// Returns null if the card has no parent (locationId is null) or
  /// if the parent card is not found in the provided cards list.
  ///
  /// [allCards] should be a flat list of all instantiated GameCard objects,
  /// typically from game state hands.
  GameCard? getParent(
    GameCard childCard,
    List<GameCard> allCards,
  ) {
    if (childCard.locationId == null) {
      return null;
    }

    return allCards
        .where((card) => card.id == childCard.locationId)
        .firstOrNull;
  }

  /// Gets the root parent of the specified card (top of the hierarchy).
  ///
  /// This method traverses up the parent chain until it finds a card
  /// with no parent (locationId is null).
  ///
  /// [allCards] should be a flat list of all instantiated GameCard objects,
  /// typically from game state hands.
  GameCard getRootParent(
    GameCard card,
    List<GameCard> allCards,
  ) {
    GameCard current = card;

    while (true) {
      final parent = getParent(current, allCards);
      if (parent == null) {
        return current;
      }
      current = parent;
    }
  }

  /// Checks if one card is an ancestor of another card.
  ///
  /// Returns true if [potentialAncestor] is a parent, grandparent, etc.
  /// of [descendant].
  ///
  /// [allCards] should be a flat list of all instantiated GameCard objects,
  /// typically from game state hands.
  bool isAncestor(
    GameCard potentialAncestor,
    GameCard descendant,
    List<GameCard> allCards,
  ) {
    GameCard? current = descendant;

    while (current != null) {
      current = getParent(current, allCards);
      if (current?.id == potentialAncestor.id) {
        return true;
      }
    }

    return false;
  }
}