# Match Selection Architecture Analysis

## Overview

This document provides an architectural analysis of the current match selection system in the Dauntless project, including current state diagrams and recommended improvements following clean architecture principles.

## Current Architecture

### Architecture Diagram

```mermaid
graph TB
    %% UI Layer
    subgraph "UI Layer"
        MMS[MatchManagementScreen]
        CMP[CreateMatchPanel]
        OMP[OpenMatchesPanel]
        PSS[PlayerSlotsSection]
        PSI[PlayerSlotItem]
    end

    %% BLoC Layer
    subgraph "BLoC Layer"
        CMB[CreateMatchBloc]
        MSB[MatchSelectionBloc]
        MSEM[MatchSelectionEnvironmentManager]
    end

    %% Use Cases Layer
    subgraph "Use Cases"
        MSU[MatchSelectionUseCase<br/>Abstract]
        LMSU[LocalMatchSelectionUseCase<br/>File-based saves]
        NMSU[NetworkMatchSelectionUseCase<br/>Server-based]
    end

    %% Frameworks Layer
    subgraph "Frameworks"
        MSM[MatchSaveManager]
        GMM[GameMatchManager]
        UM[UserManager]
    end

    %% Data Sources
    subgraph "Data Sources"
        FS[File System<br/>J<PERSON><PERSON> saves]
        SR[ServerRepository<br/>WebSocket + HTTP]
        LR[Local Repository]
    end

    %% Connections
    MMS --> CMB
    MMS --> MSEM
    CMP --> CMB
    OMP --> MSB
    PSS --> CMB
    PSI --> CMB

    CMB --> MSEM
    CMB --> MSM
    CMB --> UM
    
    MSEM --> MSB
    MSB --> MSU
    
    MSU --> LMSU
    MSU --> NMSU
    
    LMSU --> FS
    NMSU --> SR
    
    MSM --> GMM
    MSM --> LR
    
    %% State Flow
    CMB -.->|"Match Creation"| GMM
    MSB -.->|"Match Selection"| GMM
    
    %% Data Flow Labels
    CMB -.->|"fetchOpenMatches()"| NMSU
    CMB -.->|"openNewMatch()"| NMSU
    MSB -.->|"joinMatch()"| MSU
```

### Current Components Analysis

**UI Layer:**
- `MatchManagementScreen` - Main screen coordinating match creation and selection
- `CreateMatchPanel` - Panel for creating new matches with player slot configuration  
- `OpenMatchesPanel` - Displays available matches from different sources
- `PlayerSlotsSection` & `PlayerSlotItem` - Manage player slot configuration

**BLoC Layer:**
- `CreateMatchBloc` - Handles match creation logic and player slot management
- `MatchSelectionBloc` - Manages match selection from different sources
- `MatchSelectionEnvironmentManager` - Coordinates multiple match selection sources

**Use Cases:**
- `MatchSelectionUseCase` (abstract) - Base interface for match operations
- `LocalMatchSelectionUseCase` - Handles file-based saved games from `games/{gameName}/savedGames/`
- `NetworkMatchSelectionUseCase` - Handles server-based matches via WebSocket/HTTP

**Data Sources:**
- File system (JSON saves)
- Server repository (WebSocket + HTTP API)
- Local repository for in-memory operations

### Current Issues Identified

1. **Tight Coupling**: `CreateMatchBloc` directly accesses `NetworkMatchSelectionUseCase`
2. **Mixed Responsibilities**: `CreateMatchBloc` handles both UI state and network operations
3. **Inconsistent State Management**: Multiple BLoCs managing overlapping state
4. **Limited Error Handling**: Basic error handling without retry mechanisms
5. **No Offline Support**: Network failures aren't gracefully handled
6. **Incomplete Local Implementation**: Local match selection has stub implementations
7. **Direct Dependencies**: BLoCs directly instantiate and manage use cases
8. **No Caching Strategy**: No local caching for network-fetched matches

## Recommended Improved Architecture

### Improved Architecture Diagram

```mermaid
graph TB
    %% UI Layer
    subgraph "UI Layer"
        MMS[MatchManagementScreen]
        CMP[CreateMatchPanel]
        OMP[OpenMatchesPanel]
        PSS[PlayerSlotsSection]
        PSI[PlayerSlotItem]
    end

    %% BLoC Layer - Simplified
    subgraph "BLoC Layer"
        MMB[MatchManagementBloc<br/>Single source of truth]
        MSC[MatchSourceController<br/>Manages multiple sources]
    end

    %% Domain Layer - Clean Architecture
    subgraph "Domain Layer"
        subgraph "Entities"
            GM[GameMatch]
            MS[MatchSource]
            MP[MatchPreferences]
        end
        
        subgraph "Use Cases"
            FMUC[FetchMatchesUseCase]
            CMUC[CreateMatchUseCase]
            JMUC[JoinMatchUseCase]
            SMUC[SaveMatchUseCase]
        end
        
        subgraph "Repositories (Interfaces)"
            MR[MatchRepository]
            MSR[MatchSourceRepository]
            MPSR[MatchPersistenceRepository]
        end
    end

    %% Infrastructure Layer
    subgraph "Infrastructure Layer"
        subgraph "Repository Implementations"
            LMR[LocalMatchRepository]
            NMR[NetworkMatchRepository]
            FMR[FileMatchRepository]
        end
        
        subgraph "Data Sources"
            FS[File System<br/>JSON/SQLite]
            API[REST API]
            WS[WebSocket]
            CACHE[In-Memory Cache]
        end
    end

    %% Frameworks Layer
    subgraph "Frameworks"
        GM_MGR[GameMatchManager]
        USER_MGR[UserManager]
        CONN_MGR[ConnectionManager<br/>Network state]
    end

    %% UI Connections
    MMS --> MMB
    CMP --> MMB
    OMP --> MMB
    PSS --> MMB
    PSI --> MMB

    %% BLoC to Domain
    MMB --> MSC
    MMB --> FMUC
    MMB --> CMUC
    MMB --> JMUC
    MMB --> SMUC
    
    MSC --> MSR

    %% Use Cases to Repositories
    FMUC --> MR
    CMUC --> MR
    JMUC --> MR
    SMUC --> MPSR
    
    %% Repository Interfaces to Implementations
    MR --> LMR
    MR --> NMR
    MSR --> LMR
    MSR --> NMR
    MPSR --> FMR

    %% Repository Implementations to Data Sources
    LMR --> CACHE
    NMR --> API
    NMR --> WS
    FMR --> FS
    
    %% Cross-cutting concerns
    MMB --> USER_MGR
    MMB --> CONN_MGR
    NMR --> CONN_MGR
    
    %% State Management
    MMB -.->|"Unified State"| GM_MGR
```

### Key Architectural Improvements

1. **Single Source of Truth**: Replace multiple BLoCs with unified `MatchManagementBloc`
2. **Clean Architecture**: Separate domain logic from infrastructure concerns
3. **Repository Pattern**: Abstract data sources behind interfaces
4. **Dependency Injection**: Remove direct dependencies between layers
5. **Connection Management**: Add network state awareness
6. **Caching Strategy**: Implement local caching for offline support
7. **Error Recovery**: Add retry mechanisms and graceful degradation

### Benefits of Improved Architecture

- **Testability**: Clear separation of concerns makes unit testing easier
- **Maintainability**: Reduced coupling and single responsibility principle
- **Scalability**: Easy to add new match sources (e.g., cloud saves, P2P)
- **Reliability**: Better error handling and offline support
- **Performance**: Caching reduces network calls and improves responsiveness
- **Consistency**: Single state management reduces conflicts and race conditions

### Implementation Strategy

1. **Phase 1**: Introduce repository interfaces and dependency injection
2. **Phase 2**: Consolidate BLoCs into single `MatchManagementBloc`
3. **Phase 3**: Add caching and offline support
4. **Phase 4**: Implement connection management and error recovery
5. **Phase 5**: Add new match sources and advanced features

## File Locations

- **Current Implementation**: 
  - `/lib/ui/liberator/blocs/match_selection/`
  - `/lib/frameworks/environment/match_selection_environment_manager/`
  - `/lib/ui/liberator/blocs/create_match/`
  - `/lib/ui/liberator/screens/match/create/`
  - `/lib/use_cases/match_selection_use_case.dart`

- **Key Files Analyzed**:
  - `match_selection_bloc.dart` - Current match selection logic
  - `create_match_bloc.dart` - Match creation and player management
  - `match_selection_environment_manager.dart` - Multi-source coordination
  - `match_selection_use_case.dart` - Abstract interface and implementations

---

*Generated on: 2025-08-02*
*Project: Dauntless*
*Analysis Type: Architecture Review*
